productName: launch
directories:
  output: dist_electron
files:
  - package.json
  - 'electron/main.cjs'
  - 'electron/preload.cjs'
  - 'dist/**/*'
  - './app/**/*'
win:
  target: nsis
  icon: build/icons/icon.ico
mac:
  target: dmg
  # icon: 'clearbuild/icons/icon.icns' mac需要icns类型图标
linux:
  target:
    - AppImage
    - deb
  icon: 'build/icons/icon.ico'
nsis:
  oneClick: false
  allowElevation: true # 允许提升权限
  allowToChangeInstallationDirectory: true
  installerIcon: 'build/icons/icon.ico'
  uninstallerIcon: 'build/icons/icon.ico'
  installerHeaderIcon: 'build/icons/icon.ico'
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: 'launch'

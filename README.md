# 🎯 AI流式生成思维导图

## 🔥 核心问题解决

### 问题：如何实时解析AI流式返回的原始文本数据

✅ **解决方案**：智能流式文本解析器，能够：

- 逐字符接收AI输出
- 智能识别markdown结构（`#`标题、`-`列表）
- 只在完整行时创建节点，避免重复创建
- 自动建立正确的层级关系

## 🎮 使用方法

1. **启动项目**：`pnpm dev`
2. **访问**：http://localhost:5174/
3. **测试**：点击 **🔥 真实流式** 按钮
4. **观察**：AI逐字符输出的实时解析过程

## 🔧 项目结构

```
src/
├── services/
│   └── aiStreamService.ts     # 核心流式解析服务
└── views/home/
    └── index-page.vue         # 主界面
```

## 🎯 核心优势

1. **真实性**：完全模拟真实AI的输出过程
2. **准确性**：只在完整markdown标记时创建节点
3. **高效性**：增量解析，避免重复处理
4. **扩展性**：支持各种AI服务的文本输出

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm test:unit
```

### Run End-to-End Tests with [Cypress](https://www.cypress.io/)

```sh
pnpm test:e2e:dev
```

This runs the end-to-end tests against the Vite development server.
It is much faster than the production build.

But it's still recommended to test the production build with `test:e2e` before deploying (e.g. in CI environments):

```sh
pnpm build
pnpm test:e2e
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```

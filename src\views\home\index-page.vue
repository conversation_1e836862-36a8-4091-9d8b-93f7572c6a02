<script setup lang="ts">
import MindMap from 'simple-mind-map'
import { onMounted, ref } from 'vue'
const mindMapContainer = ref<HTMLDivElement | null>(null)

onMounted(() => {
  // @ts-expect-error not found
  const mindMap = new MindMap({
    el: mindMapContainer.value,
    data: {
      data: {
        text: '根节点',
      },
      children: [{ data: { text: '子节点1' } }, { data: { text: '子节点2' } }],
    },
  })
})
</script>
<template>
  <div ref="mindMapContainer" class="home-page"></div>
</template>
<style scoped lang="scss">
.home-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // font-size: 50px;
}
#mindMapContainer * {
  margin: 0;
  padding: 0;
}
</style>

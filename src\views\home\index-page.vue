<script setup lang="ts">
import MindMap from 'simple-mind-map'
import { onMounted, ref, reactive, onUnmounted } from 'vue'
import { aiStreamService, type StreamChunk, type NodeData } from '@/services/aiStreamService'

const mindMapContainer = ref<HTMLDivElement | null>(null)
// @ts-expect-error MindMap类型定义问题
let mindMap: any = null

// 组件状态
const isGenerating = ref(false)
const currentTopic = ref('')
const error = ref('')

// 维护节点映射表，用于快速查找和更新
const nodeMap = reactive<Map<string, NodeData>>(new Map())
// 根节点数据
const rootData = reactive<NodeData>({
  data: {
    text: '点击按钮开始生成思维导图',
    uid: 'root',
    expand: true,
  },
  children: [],
})

// 初始化思维导图
onMounted(() => {
  // @ts-expect-error MindMap类型定义问题
  mindMap = new MindMap({
    el: mindMapContainer.value,
    data: rootData,
    // 启用性能模式，适合大量节点
    openPerformance: true,
    // 自动适应画布
    fit: true,
    // 设置合适的缩放比例
    scaleRatio: 0.1,
    // 设置主题
    theme: 'default',
  })

  // 监听节点树渲染完成事件
  mindMap.on('node_tree_render_end', () => {
    console.log('节点树渲染完成')
  })

  // 初始化节点映射
  nodeMap.set('root', rootData)

  // 设置AI流式服务回调
  aiStreamService.setCallbacks(handleStreamChunk, handleStreamError, handleStreamComplete)
})

// 组件卸载时清理
onUnmounted(() => {
  aiStreamService.stopStream()
  if (mindMap) {
    mindMap.destroy()
  }
})

// 处理流式数据的核心函数
const handleStreamChunk = (chunk: StreamChunk) => {
  console.log('处理流式数据:', chunk)

  switch (chunk.type) {
    case 'node_create':
      createNode(chunk)
      break
    case 'node_update':
      updateNode(chunk)
      break
    case 'node_complete':
      completeNode(chunk)
      break
    case 'tree_complete':
      completeTree()
      break
    case 'error':
      handleStreamError(new Error(chunk.data.error || '未知错误'))
      break
  }
}

// 处理流式错误
const handleStreamError = (err: Error) => {
  console.error('AI流式生成错误:', err)
  error.value = err.message
  isGenerating.value = false
}

// 处理流式完成
const handleStreamComplete = () => {
  console.log('AI流式生成完成')
  isGenerating.value = false
  if (mindMap) {
    // 自动适应画布大小
    mindMap.view.fit(50, 50, 50)
  }
}

// 创建新节点
const createNode = (chunk: StreamChunk) => {
  const { nodeId, parentId, data } = chunk

  // 创建新节点数据
  const newNode: NodeData = {
    data: {
      text: data.text || '生成中...',
      uid: nodeId,
      expand: true,
    },
    children: [],
  }

  // 添加到节点映射
  nodeMap.set(nodeId, newNode)

  if (parentId && nodeMap.has(parentId)) {
    // 添加到父节点的children中
    const parentNode = nodeMap.get(parentId)!
    parentNode.children.push(newNode)

    // 触发思维导图更新
    updateMindMap()
  } else if (nodeId === 'root') {
    // 更新根节点
    rootData.data.text = data.text
    updateMindMap()
  }
}

// 更新节点内容
const updateNode = (chunk: StreamChunk) => {
  const { nodeId, data } = chunk

  if (nodeMap.has(nodeId)) {
    const node = nodeMap.get(nodeId)!
    node.data.text = data.text

    // 触发思维导图更新
    updateMindMap()
  }
}

// 标记节点完成
const completeNode = (chunk: StreamChunk) => {
  const { nodeId } = chunk

  if (nodeMap.has(nodeId)) {
    const node = nodeMap.get(nodeId)!
    // 可以在这里添加完成状态的样式标记
    console.log(`节点 ${nodeId} 生成完成:`, node.data.text)
  }
}

// 完成整个树的生成
const completeTree = () => {
  console.log('思维导图生成完成')
  handleStreamComplete()
}

// 更新思维导图显示
const updateMindMap = () => {
  if (mindMap) {
    // 使用updateData方法进行增量更新，性能更好
    mindMap.updateData(rootData)
  }
}

// 开始AI流式生成（传统方式）
const startAIGeneration = async (topic: string = 'AI技术发展') => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    error.value = ''
    currentTopic.value = topic

    // 重置数据
    resetMindMapData()

    // 使用传统模拟数据进行测试
    await aiStreamService.simulateStream(topic)
  } catch (err) {
    handleStreamError(err as Error)
  }
}

// 🔥 真实的AI流式文本解析测试
const startRealStreamGeneration = async (topic: string = 'AI技术发展') => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    error.value = ''
    currentTopic.value = topic

    // 重置数据
    resetMindMapData()

    // 🔥 使用真实的逐字符流式解析
    await aiStreamService.simulateRealStream(topic)
  } catch (err) {
    handleStreamError(err as Error)
  }
}

// 重置思维导图数据
const resetMindMapData = () => {
  nodeMap.clear()
  rootData.data.text = '正在生成思维导图...'
  rootData.children = []
  nodeMap.set('root', rootData)
  updateMindMap()
}

// 模拟AI流式生成数据（传统方式）
const simulateAIStream = () => {
  startAIGeneration('AI技术发展趋势')
}

// 🔥 真实流式解析测试
const testRealStream = () => {
  startRealStreamGeneration(currentTopic.value || 'AI技术发展趋势')
}

// 暴露方法供外部调用
defineExpose({
  handleStreamChunk,
  simulateAIStream,
  testRealStream,
  startRealStreamGeneration,
})
</script>
<template>
  <div class="home-page">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <input
          v-model="currentTopic"
          placeholder="输入主题..."
          class="topic-input"
          :disabled="isGenerating"
        />
        <button
          @click="startAIGeneration(currentTopic || 'AI技术发展')"
          class="generate-btn"
          :disabled="isGenerating"
        >
          {{ isGenerating ? '生成中...' : '传统模式' }}
        </button>
        <button @click="testRealStream" class="real-stream-btn" :disabled="isGenerating">
          🔥 真实流式
        </button>
        <button @click="simulateAIStream" class="test-btn" :disabled="isGenerating">
          快速测试
        </button>
      </div>

      <div class="status-group">
        <div class="status">
          <span>节点数量: {{ nodeMap.size }}</span>
        </div>
        <div v-if="error" class="error">
          {{ error }}
        </div>
      </div>
    </div>

    <!-- 思维导图容器 -->
    <div ref="mindMapContainer" class="mind-map-container"></div>
  </div>
</template>
<style scoped lang="scss">
.home-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 300px;
}

.control-group {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.topic-input {
  flex: 1;
  min-width: 150px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: #007bff;
  }

  &:disabled {
    background: #f5f5f5;
    cursor: not-allowed;
  }
}

.generate-btn,
.real-stream-btn,
.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  white-space: nowrap;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    transform: translateY(-1px);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }
}

.generate-btn {
  background: #28a745;
  color: white;

  &:not(:disabled):hover {
    background: #218838;
  }
}

.real-stream-btn {
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);

  &:not(:disabled):hover {
    background: linear-gradient(45deg, #ff5252, #ff9800);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
  }
}

.test-btn {
  background: #007bff;
  color: white;

  &:not(:disabled):hover {
    background: #0056b3;
  }
}

.status-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status {
  font-size: 14px;
  color: #666;

  span {
    padding: 4px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
}

.error {
  font-size: 14px;
  color: #dc3545;
  background: #f8d7da;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
}

.mind-map-container {
  flex: 1;
  width: 100%;
  height: 100%;

  * {
    margin: 0;
    padding: 0;
  }
}
</style>

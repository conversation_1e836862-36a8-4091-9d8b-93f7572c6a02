const { app, globalShortcut, ipcMain, BrowserWindow, screen } = require('electron')
const os = require('os')

const path = require('path')
const dotenv = require('dotenv')

dotenv.config()

const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  // 如果我们无法获取单实例锁，则退出当前实例
  app.quit()
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，我们应该聚焦到主窗口
    if (win) {
      if (win.isMinimized()) win.restore()
      win.focus()
    }
  })
}
let win

const createWindow = async (x = 1080, y = 1920) => {
  win = new BrowserWindow({
    x,
    y,
    width: 1080,
    height: 1920,
    frame: false, // 无边框
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      // nodeIntegration: true,
      // contextIsolation: true,
      // enableRemoteModule: true,
    },
  })
  win.show() // 显示并聚焦于窗口
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口
  // 在Electron的主进程中
  const isDev = process.env.ELECTRON_IS_DEV === 'true'
  console.log('isDev', isDev)
  if (isDev) {
    // 开发环境配置
    win.loadURL(process.env.VITE_DEV_SERVER_URL)

    win.webContents.openDevTools() // 打开开发者工具

    // 指定本地扩展文件路径（需要实际路径）

    // try {
    //   await session.defaultSession.loadExtension(
    //     path.resolve(__dirname, '../../Vuejs-devtools-6.6.1'),
    //   )
    // } catch (err) {
    //   console.log('Unable to load Vue DevTools111: ', err)
    // }
  } else {
    // 生产环境配置
    // win.loadURL('http://192.168.1.23:5174/')
    win.loadFile(path.join(__dirname, '../dist/index.html'))
  }
}

app.whenReady().then(() => {
  createWindow(1920 + 50, -835 + 50)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('ready', () => {
  // 创建 Electron 窗口等其他操作
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow()?.webContents.openDevTools()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

/**
 * AI流式生成思维导图服务 - 重构版本
 */

export interface StreamChunk {
  type: 'node_create' | 'node_update' | 'node_complete' | 'tree_complete' | 'error'
  nodeId: string
  parentId?: string
  data: {
    text: string
    isComplete?: boolean
    error?: string
  }
  timestamp?: number
}

export interface NodeData {
  data: {
    text: string
    uid: string
    expand?: boolean
  }
  children: NodeData[]
}

interface ParseState {
  buffer: string
  nodeStack: Array<{ id: string; level: number; text: string }>
  nodeCounter: number
  processedLines: number
}

export class AIStreamService {
  private onChunkCallback: ((chunk: StreamChunk) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null
  private onCompleteCallback: (() => void) | null = null

  private parseState: ParseState = {
    buffer: '',
    nodeStack: [],
    nodeCounter: 0,
    processedLines: 0
  }

  setCallbacks(
    onChunk: (chunk: StreamChunk) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ) {
    this.onChunkCallback = onChunk
    this.onErrorCallback = onError || null
    this.onCompleteCallback = onComplete || null
  }

  private resetParseState() {
    this.parseState = {
      buffer: '',
      nodeStack: [{ id: 'root', level: 0, text: '' }],
      nodeCounter: 1,
      processedLines: 0
    }
  }

  /**
   * 🔥 核心解析方法 - 修复版本
   */
  private parseStreamText(newText: string): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 添加新文本到缓冲区
    this.parseState.buffer += newText

    // 按行分割
    const lines = this.parseState.buffer.split('\n')
    console.log('解析行数:', lines);

    // 处理新的完整行（除了最后一行，因为可能不完整）
    for (let i = this.parseState.processedLines; i < lines.length - 1; i++) {
      const line = lines[i].trim()
      if (line) {
        const lineChunks = this.parseLine(line)
        chunks.push(...lineChunks)
      }
    }

    // 更新已处理的行数
    this.parseState.processedLines = Math.max(0, lines.length - 1)

    return chunks
  }

  /**
   * 解析单行 - 只处理完整的markdown标记
   */
  private parseLine(line: string): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 检测标题 (# ## ###)
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      const level = headerMatch[1].length
      const text = headerMatch[2].trim()
      return this.createNode(level, text)
    }

    // 检测列表项 (- * +)
    const listMatch = line.match(/^(\s*)([-*+])\s+(.+)$/)
    if (listMatch) {
      const indent = listMatch[1].length
      const text = listMatch[3].trim()
      const level = Math.floor(indent / 2) + 2 // 列表从level 2开始
      return this.createNode(level, text)
    }

    return chunks
  }

  /**
   * 创建节点 - 处理层级关系
   */
  private createNode(level: number, text: string): StreamChunk[] {
    const chunks: StreamChunk[] = []
    const nodeId = `node_${this.parseState.nodeCounter++}`

    // 清理节点栈 - 移除级别大于等于当前级别的节点
    while (
      this.parseState.nodeStack.length > 0 &&
      this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
    ) {
      const completedNode = this.parseState.nodeStack.pop()
      if (completedNode && completedNode.id !== 'root') {
        chunks.push({
          type: 'node_complete',
          nodeId: completedNode.id,
          data: { text: completedNode.text, isComplete: true },
          timestamp: Date.now()
        })
      }
    }

    // 确定父节点
    const parentId = this.parseState.nodeStack.length > 0
      ? this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id
      : 'root'

    // 创建新节点
    chunks.push({
      type: 'node_create',
      nodeId,
      parentId,
      data: { text, isComplete: true },
      timestamp: Date.now()
    })

    // 将新节点加入栈
    this.parseState.nodeStack.push({ id: nodeId, level, text })

    return chunks
  }

  /**
   * 完成所有节点
   */
  private finalizeAllNodes() {
    while (this.parseState.nodeStack.length > 0) {
      const node = this.parseState.nodeStack.pop()
      if (node && node.id !== 'root') {
        this.handleChunk({
          type: 'node_complete',
          nodeId: node.id,
          data: { text: node.text, isComplete: true },
          timestamp: Date.now()
        })
      }
    }

    this.handleChunk({
      type: 'tree_complete',
      nodeId: '',
      data: { text: '' },
      timestamp: Date.now()
    })
  }

  private handleChunk(chunk: StreamChunk) {
    if (this.onChunkCallback) {
      this.onChunkCallback(chunk)
    }
  }

  private handleError(error: Error) {
    if (this.onErrorCallback) {
      this.onErrorCallback(error)
    }
  }

  private handleComplete() {
    if (this.onCompleteCallback) {
      this.onCompleteCallback()
    }
  }

  /**
   * 🔥 真实流式模拟 - 逐字符输出
   */
  simulateRealStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      this.resetParseState()

      const fullText = `# ${topic}思维导图

## 核心技术
### 机器学习
  #### 监督学习
  #### 无监督学习
### 深度学习
  #### 神经网络
  #### 卷积神经网络
### 自然语言处理
  #### 文本分析
  #### 语音识别`

      let currentIndex = 0

      const streamInterval = setInterval(() => {
        if (currentIndex >= fullText.length) {
          clearInterval(streamInterval)
          this.finalizeAllNodes()
          resolve()
          return
        }

        // 每次输出1-3个字符
        const chunkSize = Math.random() > 0.7 ? 3 : Math.random() > 0.5 ? 2 : 1
        const textChunk = fullText.slice(currentIndex, currentIndex + chunkSize)
        currentIndex += chunkSize

        // 解析新文本
        const chunks = this.parseStreamText(textChunk)

        for (const chunk of chunks) {
          this.handleChunk(chunk)
        }

      }, 50)
    })
  }

  stopStream() {
    // 停止流式传输的清理工作
  }
}

export const aiStreamService = new AIStreamService()

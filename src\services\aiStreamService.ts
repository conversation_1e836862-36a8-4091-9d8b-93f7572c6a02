/**
 * AI流式生成思维导图服务 - 实时解析版本
 */

// 流式数据块接口
export interface StreamChunk {
  type: 'node_create' | 'node_update' | 'node_complete' | 'tree_complete' | 'error'
  nodeId: string
  parentId?: string
  data: {
    text: string
    isComplete?: boolean
    uid?: string
    error?: string
  }
  position?: 'before' | 'after' | 'child'
  timestamp?: number
}

// 解析状态接口
interface ParseState {
  buffer: string
  currentLevel: number
  nodeStack: Array<{ id: string; level: number; text: string }>
  nodeCounter: number
  lastProcessedIndex: number
}

// 节点数据接口
export interface NodeData {
  data: {
    text: string
    uid: string
    expand?: boolean
    isGenerating?: boolean
  }
  children: NodeData[]
}

// AI流式服务类
export class AIStreamService {
  private eventSource: EventSource | null = null
  private onChunkCallback: ((chunk: StreamChunk) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null
  private onCompleteCallback: (() => void) | null = null

  // 流式解析状态
  private parseState: ParseState = {
    buffer: '',
    currentLevel: 0,
    nodeStack: [],
    nodeCounter: 0,
    lastProcessedIndex: 0
  }

  /**
   * 设置回调函数
   */
  setCallbacks(
    onChunk: (chunk: StreamChunk) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ) {
    this.onChunkCallback = onChunk
    this.onErrorCallback = onError || null
    this.onCompleteCallback = onComplete || null
  }

  /**
   * 重置解析状态
   */
  private resetParseState() {
    this.parseState = {
      buffer: '',
      currentLevel: 0,
      nodeStack: [{ id: 'root', level: 0, text: '' }],
      nodeCounter: 1,
      lastProcessedIndex: 0
    }
  }

  /**
   * 核心方法：实时解析AI流式文本
   * 这是关键！AI每输出一个字符，我们就尝试解析
   */
  private parseStreamText(newText: string): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 将新文本添加到缓冲区
    this.parseState.buffer += newText

    // 从上次处理的位置开始解析
    const textToProcess = this.parseState.buffer.slice(this.parseState.lastProcessedIndex)

    // 按行分割，但保留不完整的行
    const lines = textToProcess.split('\n')
    const completeLines = lines.slice(0, -1) // 完整的行
    const incompleteLine = lines[lines.length - 1] // 可能不完整的行

    // 处理完整的行
    for (const line of completeLines) {
      const lineChunks = this.parseLine(line)
      chunks.push(...lineChunks)
    }

    // 如果不完整的行看起来像是一个节点的开始，也尝试解析
    if (incompleteLine.trim()) {
      const partialChunks = this.parsePartialLine(incompleteLine)
      chunks.push(...partialChunks)
    }

    // 更新已处理的位置
    this.parseState.lastProcessedIndex = this.parseState.buffer.length - incompleteLine.length

    return chunks
  }

  /**
   * 解析完整的行
   */
  private parseLine(line: string): StreamChunk[] {
    const chunks: StreamChunk[] = []
    const trimmedLine = line.trim()

    if (!trimmedLine) return chunks

    // 检测标题级别 (# ## ### 等)
    const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      const level = headerMatch[1].length
      const text = headerMatch[2].trim()

      return this.createNodeFromHeader(level, text)
    }

    // 检测列表项 (- * + 或数字列表)
    const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/)
    if (listMatch) {
      const indent = listMatch[1].length
      const text = listMatch[3].trim()
      const level = Math.floor(indent / 2) + 2 // 列表项从level 2开始

      return this.createNodeFromListItem(level, text)
    }

    // 普通文本行，可能是根节点内容的延续
    if (this.parseState.nodeStack.length > 0) {
      const currentNode = this.parseState.nodeStack[this.parseState.nodeStack.length - 1]
      chunks.push({
        type: 'node_update',
        nodeId: currentNode.id,
        data: {
          text: currentNode.text + (currentNode.text ? ' ' : '') + trimmedLine,
          isComplete: false
        },
        timestamp: Date.now()
      })

      // 更新节点栈中的文本
      currentNode.text += (currentNode.text ? ' ' : '') + trimmedLine
    }

    return chunks
  }

  /**
   * 解析部分行（实时输入中）
   */
  private parsePartialLine(line: string): StreamChunk[] {
    const chunks: StreamChunk[] = []
    const trimmedLine = line.trim()

    if (!trimmedLine) return chunks

    // 检测是否是标题的开始
    if (trimmedLine.match(/^#{1,6}/)) {
      const headerMatch = trimmedLine.match(/^(#{1,6})\s*(.*)$/)
      if (headerMatch) {
        const level = headerMatch[1].length
        const text = headerMatch[2].trim()

        if (text) {
          return this.createNodeFromHeader(level, text, false) // 标记为未完成
        }
      }
    }

    // 检测是否是列表项的开始
    if (trimmedLine.match(/^(\s*)([-*+]|\d+\.)/)) {
      const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s*(.*)$/)
      if (listMatch) {
        const indent = listMatch[1].length
        const text = listMatch[3].trim()
        const level = Math.floor(indent / 2) + 2

        if (text) {
          return this.createNodeFromListItem(level, text, false) // 标记为未完成
        }
      }
    }

    // 如果是普通文本的延续，更新当前节点
    if (this.parseState.nodeStack.length > 0) {
      const currentNode = this.parseState.nodeStack[this.parseState.nodeStack.length - 1]
      const newText = currentNode.text + (currentNode.text ? ' ' : '') + trimmedLine

      chunks.push({
        type: 'node_update',
        nodeId: currentNode.id,
        data: {
          text: newText,
          isComplete: false
        },
        timestamp: Date.now()
      })

      currentNode.text = newText
    }

    return chunks
  }

  /**
   * 从标题创建节点
   */
  private createNodeFromHeader(level: number, text: string, isComplete: boolean = true): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 生成唯一ID
    const nodeId = `node_${this.parseState.nodeCounter++}`

    // 找到父节点
    let parentId = 'root'

    // 清理节点栈，移除所有级别大于等于当前级别的节点
    while (
      this.parseState.nodeStack.length > 0 &&
      this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
    ) {
      // 标记上一个节点为完成
      const lastNode = this.parseState.nodeStack.pop()
      if (lastNode) {
        chunks.push({
          type: 'node_complete',
          nodeId: lastNode.id,
          data: {
            text: lastNode.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 如果栈不为空，取栈顶节点作为父节点
    if (this.parseState.nodeStack.length > 0) {
      parentId = this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id
    }

    // 创建新节点
    chunks.push({
      type: 'node_create',
      nodeId,
      parentId,
      data: {
        text,
        isComplete
      },
      timestamp: Date.now()
    })

    // 将新节点加入栈
    this.parseState.nodeStack.push({
      id: nodeId,
      level,
      text
    })

    return chunks
  }

  /**
   * 从列表项创建节点
   */
  private createNodeFromListItem(level: number, text: string, isComplete: boolean = true): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 生成唯一ID
    const nodeId = `node_${this.parseState.nodeCounter++}`

    // 找到父节点
    let parentId = 'root'

    // 清理节点栈，移除所有级别大于等于当前级别的节点
    while (
      this.parseState.nodeStack.length > 0 &&
      this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
    ) {
      // 标记上一个节点为完成
      const lastNode = this.parseState.nodeStack.pop()
      if (lastNode) {
        chunks.push({
          type: 'node_complete',
          nodeId: lastNode.id,
          data: {
            text: lastNode.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 如果栈不为空，取栈顶节点作为父节点
    if (this.parseState.nodeStack.length > 0) {
      parentId = this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id
    }

    // 创建新节点
    chunks.push({
      type: 'node_create',
      nodeId,
      parentId,
      data: {
        text,
        isComplete
      },
      timestamp: Date.now()
    })

    // 将新节点加入栈
    this.parseState.nodeStack.push({
      id: nodeId,
      level,
      text
    })

    return chunks
  }

  /**
   * 开始AI流式生成
   * @param prompt 用户输入的提示词
   * @param apiUrl API地址
   */
  async startStream(prompt: string, apiUrl: string = '/api/ai/stream') {
    try {
      // 方式1: 使用EventSource (Server-Sent Events)
      if (apiUrl.includes('sse')) {
        this.startSSEStream(prompt, apiUrl)
      } else {
        // 方式2: 使用fetch流式读取
        await this.startFetchStream(prompt, apiUrl)
      }
    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 使用Server-Sent Events方式
   */
  private startSSEStream(prompt: string, apiUrl: string) {
    const url = new URL(apiUrl, window.location.origin)
    url.searchParams.set('prompt', prompt)

    this.eventSource = new EventSource(url.toString())

    this.eventSource.onmessage = (event) => {
      try {
        const chunk: StreamChunk = JSON.parse(event.data)
        chunk.timestamp = Date.now()
        this.handleChunk(chunk)
      } catch (error) {
        console.error('解析SSE数据失败:', error)
      }
    }

    this.eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)
      this.handleError(new Error('SSE连接失败'))
      this.stopStream()
    }
  }

  /**
   * 使用fetch流式读取方式 - 实时文本解析版本
   */
  private async startFetchStream(prompt: string, apiUrl: string) {
    // 重置解析状态
    this.resetParseState()

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt }),
    })

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          // 完成时，标记所有剩余节点为完成状态
          this.finalizeAllNodes()
          this.handleComplete()
          break
        }

        // 解码新的文本数据
        const newText = decoder.decode(value, { stream: true })

        // 🔥 关键：实时解析每一个新字符！
        const chunks = this.parseStreamText(newText)

        // 立即处理解析出的数据块
        for (const chunk of chunks) {
          this.handleChunk(chunk)
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  /**
   * 完成时标记所有节点为完成状态
   */
  private finalizeAllNodes() {
    // 标记栈中所有节点为完成
    while (this.parseState.nodeStack.length > 0) {
      const node = this.parseState.nodeStack.pop()
      if (node && node.id !== 'root') {
        this.handleChunk({
          type: 'node_complete',
          nodeId: node.id,
          data: {
            text: node.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 发送树完成信号
    this.handleChunk({
      type: 'tree_complete',
      nodeId: '',
      data: { text: '' },
      timestamp: Date.now()
    })
  }

  /**
   * 处理缓冲区数据，提取完整的JSON对象
   */
  private processBuffer(buffer: string): string {
    const lines = buffer.split('\n')
    let remainingBuffer = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      if (!line) continue

      try {
        // 尝试解析JSON
        const chunk: StreamChunk = JSON.parse(line)
        chunk.timestamp = Date.now()
        this.handleChunk(chunk)
      } catch (error) {
        // 如果是最后一行且解析失败，保留到缓冲区
        if (i === lines.length - 1) {
          remainingBuffer = line
        } else {
          console.warn('跳过无效的JSON行:', line)
        }
      }
    }

    return remainingBuffer
  }

  /**
   * 处理单个数据块
   */
  private handleChunk(chunk: StreamChunk) {
    if (this.onChunkCallback) {
      this.onChunkCallback(chunk)
    }

    // 如果是完成类型，触发完成回调
    if (chunk.type === 'tree_complete') {
      this.handleComplete()
    }

    // 如果是错误类型，触发错误回调
    if (chunk.type === 'error') {
      this.handleError(new Error(chunk.data.error || '未知错误'))
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error) {
    if (this.onErrorCallback) {
      this.onErrorCallback(error)
    }
  }

  /**
   * 处理完成
   */
  private handleComplete() {
    if (this.onCompleteCallback) {
      this.onCompleteCallback()
    }
  }

  /**
   * 停止流式传输
   */
  stopStream() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }

  /**
   * 🔥 真实的AI流式文本模拟器 - 逐字输出
   * 这才是真正的流式解析测试！
   */
  simulateRealStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      // 重置解析状态
      this.resetParseState()

      // 模拟AI逐步输出的markdown文本
      const fullText = `# ${topic}思维导图

## 核心技术
- 机器学习
  - 监督学习
  - 无监督学习
  - 强化学习
- 深度学习
  - 神经网络
  - 卷积神经网络
  - 循环神经网络
- 自然语言处理
  - 文本分析
  - 语音识别
  - 机器翻译

## 应用领域
- 智能客服
  - 对话系统
  - 情感分析
- 自动驾驶
  - 环境感知
  - 路径规划
- 医疗诊断
  - 影像识别
  - 病理分析

## 发展趋势
- 多模态AI
- 边缘计算
- 联邦学习`

      let currentIndex = 0

      // 🔥 关键：模拟AI逐字符输出
      const streamInterval = setInterval(() => {
        if (currentIndex >= fullText.length) {
          clearInterval(streamInterval)

          // 完成时处理剩余节点
          this.finalizeAllNodes()
          resolve()
          return
        }

        // 每次输出1-3个字符，模拟真实的AI输出速度
        const chunkSize = Math.random() > 0.7 ? 3 : Math.random() > 0.5 ? 2 : 1
        const textChunk = fullText.slice(currentIndex, currentIndex + chunkSize)
        currentIndex += chunkSize

        // 🔥 实时解析新的文本块
        const chunks = this.parseStreamText(textChunk)

        // 立即处理解析出的数据块
        for (const chunk of chunks) {
          this.handleChunk(chunk)
        }

      }, 50) // 每50ms输出一次，模拟真实的流式速度
    })
  }

  /**
   * 传统的模拟方法（保留用于对比）
   */
  simulateStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      const chunks: StreamChunk[] = [
        {
          type: 'node_update',
          nodeId: 'root',
          data: { text: `${topic}思维导图` },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch1',
          parentId: 'root',
          data: { text: '核心技术' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch2',
          parentId: 'root',
          data: { text: '应用领域' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch3',
          parentId: 'root',
          data: { text: '发展趋势' },
          timestamp: Date.now()
        },
        {
          type: 'tree_complete',
          nodeId: '',
          data: { text: '' },
          timestamp: Date.now()
        }
      ]

      // 模拟流式传输，每300ms处理一个数据块
      chunks.forEach((chunk, index) => {
        setTimeout(() => {
          this.handleChunk(chunk)
          if (index === chunks.length - 1) {
            resolve()
          }
        }, index * 300)
      })
    })
  }
}

// 导出单例实例
export const aiStreamService = new AIStreamService()

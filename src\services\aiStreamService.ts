/**
 * AI流式生成思维导图服务
 */

// 流式数据块接口
export interface StreamChunk {
  type: 'node_create' | 'node_update' | 'node_complete' | 'tree_complete' | 'error'
  nodeId: string
  parentId?: string
  data: {
    text: string
    isComplete?: boolean
    uid?: string
    error?: string
  }
  position?: 'before' | 'after' | 'child'
  timestamp?: number
}

// 节点数据接口
export interface NodeData {
  data: {
    text: string
    uid: string
    expand?: boolean
    isGenerating?: boolean
  }
  children: NodeData[]
}

// AI流式服务类
export class AIStreamService {
  private eventSource: EventSource | null = null
  private onChunkCallback: ((chunk: StreamChunk) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null
  private onCompleteCallback: (() => void) | null = null

  /**
   * 设置回调函数
   */
  setCallbacks(
    onChunk: (chunk: StreamChunk) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ) {
    this.onChunkCallback = onChunk
    this.onErrorCallback = onError
    this.onCompleteCallback = onComplete
  }

  /**
   * 开始AI流式生成
   * @param prompt 用户输入的提示词
   * @param apiUrl API地址
   */
  async startStream(prompt: string, apiUrl: string = '/api/ai/stream') {
    try {
      // 方式1: 使用EventSource (Server-Sent Events)
      if (apiUrl.includes('sse')) {
        this.startSSEStream(prompt, apiUrl)
      } else {
        // 方式2: 使用fetch流式读取
        await this.startFetchStream(prompt, apiUrl)
      }
    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 使用Server-Sent Events方式
   */
  private startSSEStream(prompt: string, apiUrl: string) {
    const url = new URL(apiUrl, window.location.origin)
    url.searchParams.set('prompt', prompt)

    this.eventSource = new EventSource(url.toString())

    this.eventSource.onmessage = (event) => {
      try {
        const chunk: StreamChunk = JSON.parse(event.data)
        chunk.timestamp = Date.now()
        this.handleChunk(chunk)
      } catch (error) {
        console.error('解析SSE数据失败:', error)
      }
    }

    this.eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)
      this.handleError(new Error('SSE连接失败'))
      this.stopStream()
    }
  }

  /**
   * 使用fetch流式读取方式
   */
  private async startFetchStream(prompt: string, apiUrl: string) {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt }),
    })

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          // 处理缓冲区中剩余的数据
          if (buffer.trim()) {
            this.processBuffer(buffer)
          }
          this.handleComplete()
          break
        }

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true })
        
        // 处理完整的JSON对象
        buffer = this.processBuffer(buffer)
      }
    } finally {
      reader.releaseLock()
    }
  }

  /**
   * 处理缓冲区数据，提取完整的JSON对象
   */
  private processBuffer(buffer: string): string {
    const lines = buffer.split('\n')
    let remainingBuffer = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      if (!line) continue

      try {
        // 尝试解析JSON
        const chunk: StreamChunk = JSON.parse(line)
        chunk.timestamp = Date.now()
        this.handleChunk(chunk)
      } catch (error) {
        // 如果是最后一行且解析失败，保留到缓冲区
        if (i === lines.length - 1) {
          remainingBuffer = line
        } else {
          console.warn('跳过无效的JSON行:', line)
        }
      }
    }

    return remainingBuffer
  }

  /**
   * 处理单个数据块
   */
  private handleChunk(chunk: StreamChunk) {
    if (this.onChunkCallback) {
      this.onChunkCallback(chunk)
    }

    // 如果是完成类型，触发完成回调
    if (chunk.type === 'tree_complete') {
      this.handleComplete()
    }

    // 如果是错误类型，触发错误回调
    if (chunk.type === 'error') {
      this.handleError(new Error(chunk.data.error || '未知错误'))
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error) {
    if (this.onErrorCallback) {
      this.onErrorCallback(error)
    }
  }

  /**
   * 处理完成
   */
  private handleComplete() {
    if (this.onCompleteCallback) {
      this.onCompleteCallback()
    }
  }

  /**
   * 停止流式传输
   */
  stopStream() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }

  /**
   * 模拟AI流式数据（用于测试）
   */
  simulateStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      const chunks: StreamChunk[] = [
        {
          type: 'node_update',
          nodeId: 'root',
          data: { text: `${topic}思维导图` },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch1',
          parentId: 'root',
          data: { text: '核心技术' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch2',
          parentId: 'root',
          data: { text: '应用领域' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch3',
          parentId: 'root',
          data: { text: '发展趋势' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub1-1',
          parentId: 'branch1',
          data: { text: '机器学习' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub1-2',
          parentId: 'branch1',
          data: { text: '深度学习' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub1-3',
          parentId: 'branch1',
          data: { text: '自然语言处理' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub2-1',
          parentId: 'branch2',
          data: { text: '智能客服' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub2-2',
          parentId: 'branch2',
          data: { text: '自动驾驶' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'sub2-3',
          parentId: 'branch2',
          data: { text: '医疗诊断' },
          timestamp: Date.now()
        },
        {
          type: 'tree_complete',
          nodeId: '',
          data: { text: '' },
          timestamp: Date.now()
        }
      ]

      // 模拟流式传输，每300ms处理一个数据块
      chunks.forEach((chunk, index) => {
        setTimeout(() => {
          this.handleChunk(chunk)
          if (index === chunks.length - 1) {
            resolve()
          }
        }, index * 300)
      })
    })
  }
}

// 导出单例实例
export const aiStreamService = new AIStreamService()

/**
 * AI流式生成思维导图服务 - 实时解析版本
 */

// 流式数据块接口
export interface StreamChunk {
  type: 'node_create' | 'node_update' | 'node_complete' | 'tree_complete' | 'error'
  nodeId: string
  parentId?: string
  data: {
    text: string
    isComplete?: boolean
    uid?: string
    error?: string
  }
  position?: 'before' | 'after' | 'child'
  timestamp?: number
}

// 解析状态接口
interface ParseState {
  buffer: string
  currentLevel: number
  nodeStack: Array<{ id: string; level: number; text: string }>
  nodeCounter: number
  lastProcessedLineIndex: number
  currentNodeId: string | null
  currentNodeText: string
}

// 节点数据接口
export interface NodeData {
  data: {
    text: string
    uid: string
    expand?: boolean
    isGenerating?: boolean
  }
  children: NodeData[]
}

// AI流式服务类
export class AIStreamService {
  private eventSource: EventSource | null = null
  private onChunkCallback: ((chunk: StreamChunk) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null
  private onCompleteCallback: (() => void) | null = null

  // 流式解析状态
  private parseState: ParseState = {
    buffer: '',
    currentLevel: 0,
    nodeStack: [],
    nodeCounter: 0,
    lastProcessedLineIndex: 0,
    currentNodeId: null,
    currentNodeText: ''
  }

  /**
   * 设置回调函数
   */
  setCallbacks(
    onChunk: (chunk: StreamChunk) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ) {
    this.onChunkCallback = onChunk
    this.onErrorCallback = onError || null
    this.onCompleteCallback = onComplete || null
  }

  /**
   * 重置解析状态
   */
  private resetParseState() {
    this.parseState = {
      buffer: '',
      currentLevel: 0,
      nodeStack: [{ id: 'root', level: 0, text: '' }],
      nodeCounter: 1,
      lastProcessedLineIndex: 0,
      currentNodeId: null,
      currentNodeText: ''
    }
  }

  /**
   * 🔥 核心方法：智能流式文本解析
   * 只在遇到完整的markdown标记时才创建/更新节点
   */
  private parseStreamText(newText: string): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 将新文本添加到缓冲区
    this.parseState.buffer += newText

    // 按行分割
    const lines = this.parseState.buffer.split('\n')

    // 处理新的完整行
    for (let i = this.parseState.lastProcessedLineIndex; i < lines.length - 1; i++) {
      const line = lines[i]
      const lineChunks = this.processCompleteLine(line)
      chunks.push(...lineChunks)
    }

    // 更新已处理的行索引
    this.parseState.lastProcessedLineIndex = Math.max(0, lines.length - 1)

    // 处理当前正在输入的行（最后一行）
    const currentLine = lines[lines.length - 1]
    if (currentLine.trim()) {
      const partialChunks = this.processPartialLine(currentLine)
      chunks.push(...partialChunks)
    }

    return chunks
  }

  /**
   * 处理完整的行 - 确定创建节点
   */
  private processCompleteLine(line: string): StreamChunk[] {
    const chunks: StreamChunk[] = []
    const trimmedLine = line.trim()

    if (!trimmedLine) return chunks

    // 检测标题 (# ## ###)
    const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      const level = headerMatch[1].length
      const text = headerMatch[2].trim()
      return this.createNodeFromHeader(level, text, true)
    }

    // 检测列表项 (- * + 或数字列表)
    const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/)
    if (listMatch) {
      const indent = listMatch[1].length
      const text = listMatch[3].trim()
      const level = Math.floor(indent / 2) + 2
      return this.createNodeFromListItem(level, text, true)
    }

    return chunks
  }

  /**
   * 处理部分行 - 实时更新当前节点
   */
  private processPartialLine(line: string): StreamChunk[] {
    const chunks: StreamChunk[] = []
    const trimmedLine = line.trim()

    if (!trimmedLine) return chunks

    // 检测是否是新的标题开始
    if (trimmedLine.match(/^#{1,6}(\s|$)/)) {
      const headerMatch = trimmedLine.match(/^(#{1,6})\s*(.*)$/)
      if (headerMatch) {
        const level = headerMatch[1].length
        const text = headerMatch[2].trim()

        if (text) {
          // 如果有文本内容，创建或更新节点
          return this.updateOrCreateNode('header', level, text, false)
        }
      }
    }

    // 检测是否是新的列表项开始
    else if (trimmedLine.match(/^(\s*)([-*+]|\d+\.)(\s|$)/)) {
      const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s*(.*)$/)
      if (listMatch) {
        const indent = listMatch[1].length
        const text = listMatch[3].trim()
        const level = Math.floor(indent / 2) + 2

        if (text) {
          // 如果有文本内容，创建或更新节点
          return this.updateOrCreateNode('list', level, text, false)
        }
      }
    }

    return chunks
  }

  /**
   * 智能节点更新或创建
   */
  private updateOrCreateNode(type: 'header' | 'list', level: number, text: string, isComplete: boolean): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 如果当前正在编辑同一个节点，只更新文本
    if (this.parseState.currentNodeId && this.parseState.currentNodeText !== text) {
      chunks.push({
        type: 'node_update',
        nodeId: this.parseState.currentNodeId,
        data: {
          text,
          isComplete
        },
        timestamp: Date.now()
      })

      this.parseState.currentNodeText = text

      // 更新节点栈中的文本
      const currentNode = this.parseState.nodeStack.find(n => n.id === this.parseState.currentNodeId)
      if (currentNode) {
        currentNode.text = text
      }
    } else if (!this.parseState.currentNodeId) {
      // 创建新节点
      if (type === 'header') {
        const nodeChunks = this.createNodeFromHeader(level, text, isComplete)
        chunks.push(...nodeChunks)
      } else {
        const nodeChunks = this.createNodeFromListItem(level, text, isComplete)
        chunks.push(...nodeChunks)
      }

      // 记录当前节点
      if (chunks.length > 0 && chunks[chunks.length - 1].type === 'node_create') {
        this.parseState.currentNodeId = chunks[chunks.length - 1].nodeId
        this.parseState.currentNodeText = text
      }
    }

    return chunks
  }



  /**
   * 从标题创建节点
   */
  private createNodeFromHeader(level: number, text: string, isComplete: boolean = true): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 如果有当前正在编辑的节点，先完成它
    if (this.parseState.currentNodeId) {
      chunks.push({
        type: 'node_complete',
        nodeId: this.parseState.currentNodeId,
        data: {
          text: this.parseState.currentNodeText,
          isComplete: true
        },
        timestamp: Date.now()
      })

      // 清空当前节点状态
      this.parseState.currentNodeId = null
      this.parseState.currentNodeText = ''
    }

    // 生成唯一ID
    const nodeId = `node_${this.parseState.nodeCounter++}`

    // 找到父节点
    let parentId = 'root'

    // 清理节点栈，移除所有级别大于等于当前级别的节点
    while (
      this.parseState.nodeStack.length > 0 &&
      this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
    ) {
      const lastNode = this.parseState.nodeStack.pop()
      if (lastNode && lastNode.id !== 'root') {
        chunks.push({
          type: 'node_complete',
          nodeId: lastNode.id,
          data: {
            text: lastNode.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 如果栈不为空，取栈顶节点作为父节点
    if (this.parseState.nodeStack.length > 0) {
      parentId = this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id
    }

    // 创建新节点
    chunks.push({
      type: 'node_create',
      nodeId,
      parentId,
      data: {
        text,
        isComplete
      },
      timestamp: Date.now()
    })

    // 将新节点加入栈
    this.parseState.nodeStack.push({
      id: nodeId,
      level,
      text
    })

    // 如果节点未完成，记录为当前节点
    if (!isComplete) {
      this.parseState.currentNodeId = nodeId
      this.parseState.currentNodeText = text
    }

    return chunks
  }

  /**
   * 从列表项创建节点
   */
  private createNodeFromListItem(level: number, text: string, isComplete: boolean = true): StreamChunk[] {
    const chunks: StreamChunk[] = []

    // 如果有当前正在编辑的节点，先完成它
    if (this.parseState.currentNodeId) {
      chunks.push({
        type: 'node_complete',
        nodeId: this.parseState.currentNodeId,
        data: {
          text: this.parseState.currentNodeText,
          isComplete: true
        },
        timestamp: Date.now()
      })

      // 清空当前节点状态
      this.parseState.currentNodeId = null
      this.parseState.currentNodeText = ''
    }

    // 生成唯一ID
    const nodeId = `node_${this.parseState.nodeCounter++}`

    // 找到父节点
    let parentId = 'root'

    // 清理节点栈，移除所有级别大于等于当前级别的节点
    while (
      this.parseState.nodeStack.length > 0 &&
      this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
    ) {
      const lastNode = this.parseState.nodeStack.pop()
      if (lastNode && lastNode.id !== 'root') {
        chunks.push({
          type: 'node_complete',
          nodeId: lastNode.id,
          data: {
            text: lastNode.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 如果栈不为空，取栈顶节点作为父节点
    if (this.parseState.nodeStack.length > 0) {
      parentId = this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id
    }

    // 创建新节点
    chunks.push({
      type: 'node_create',
      nodeId,
      parentId,
      data: {
        text,
        isComplete
      },
      timestamp: Date.now()
    })

    // 将新节点加入栈
    this.parseState.nodeStack.push({
      id: nodeId,
      level,
      text
    })

    // 如果节点未完成，记录为当前节点
    if (!isComplete) {
      this.parseState.currentNodeId = nodeId
      this.parseState.currentNodeText = text
    }

    return chunks
  }

  /**
   * 开始AI流式生成
   * @param prompt 用户输入的提示词
   * @param apiUrl API地址
   */
  async startStream(prompt: string, apiUrl: string = '/api/ai/stream') {
    try {
      // 方式1: 使用EventSource (Server-Sent Events)
      if (apiUrl.includes('sse')) {
        this.startSSEStream(prompt, apiUrl)
      } else {
        // 方式2: 使用fetch流式读取
        await this.startFetchStream(prompt, apiUrl)
      }
    } catch (error) {
      this.handleError(error as Error)
    }
  }

  /**
   * 使用Server-Sent Events方式
   */
  private startSSEStream(prompt: string, apiUrl: string) {
    const url = new URL(apiUrl, window.location.origin)
    url.searchParams.set('prompt', prompt)

    this.eventSource = new EventSource(url.toString())

    this.eventSource.onmessage = (event) => {
      try {
        const chunk: StreamChunk = JSON.parse(event.data)
        chunk.timestamp = Date.now()
        this.handleChunk(chunk)
      } catch (error) {
        console.error('解析SSE数据失败:', error)
      }
    }

    this.eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)
      this.handleError(new Error('SSE连接失败'))
      this.stopStream()
    }
  }

  /**
   * 使用fetch流式读取方式 - 实时文本解析版本
   */
  private async startFetchStream(prompt: string, apiUrl: string) {
    // 重置解析状态
    this.resetParseState()

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt }),
    })

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          // 完成时，标记所有剩余节点为完成状态
          this.finalizeAllNodes()
          this.handleComplete()
          break
        }

        // 解码新的文本数据
        const newText = decoder.decode(value, { stream: true })

        // 🔥 关键：实时解析每一个新字符！
        const chunks = this.parseStreamText(newText)

        // 立即处理解析出的数据块
        for (const chunk of chunks) {
          this.handleChunk(chunk)
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  /**
   * 完成时标记所有节点为完成状态
   */
  private finalizeAllNodes() {
    // 如果有当前正在编辑的节点，先完成它
    if (this.parseState.currentNodeId) {
      this.handleChunk({
        type: 'node_complete',
        nodeId: this.parseState.currentNodeId,
        data: {
          text: this.parseState.currentNodeText,
          isComplete: true
        },
        timestamp: Date.now()
      })

      this.parseState.currentNodeId = null
      this.parseState.currentNodeText = ''
    }

    // 标记栈中所有节点为完成
    while (this.parseState.nodeStack.length > 0) {
      const node = this.parseState.nodeStack.pop()
      if (node && node.id !== 'root') {
        this.handleChunk({
          type: 'node_complete',
          nodeId: node.id,
          data: {
            text: node.text,
            isComplete: true
          },
          timestamp: Date.now()
        })
      }
    }

    // 发送树完成信号
    this.handleChunk({
      type: 'tree_complete',
      nodeId: '',
      data: { text: '' },
      timestamp: Date.now()
    })
  }



  /**
   * 处理单个数据块
   */
  private handleChunk(chunk: StreamChunk) {
    if (this.onChunkCallback) {
      this.onChunkCallback(chunk)
    }

    // 如果是完成类型，触发完成回调
    if (chunk.type === 'tree_complete') {
      this.handleComplete()
    }

    // 如果是错误类型，触发错误回调
    if (chunk.type === 'error') {
      this.handleError(new Error(chunk.data.error || '未知错误'))
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error) {
    if (this.onErrorCallback) {
      this.onErrorCallback(error)
    }
  }

  /**
   * 处理完成
   */
  private handleComplete() {
    if (this.onCompleteCallback) {
      this.onCompleteCallback()
    }
  }

  /**
   * 停止流式传输
   */
  stopStream() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }

  /**
   * 🔥 真实的AI流式文本模拟器 - 逐字输出
   * 这才是真正的流式解析测试！
   */
  simulateRealStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      // 重置解析状态
      this.resetParseState()

      // 模拟AI逐步输出的markdown文本
      const fullText = `# ${topic}思维导图

## 核心技术
- 机器学习
  - 监督学习
  - 无监督学习
  - 强化学习
- 深度学习
  - 神经网络
  - 卷积神经网络
  - 循环神经网络
- 自然语言处理
  - 文本分析
  - 语音识别
  - 机器翻译

## 应用领域
- 智能客服
  - 对话系统
  - 情感分析
- 自动驾驶
  - 环境感知
  - 路径规划
- 医疗诊断
  - 影像识别
  - 病理分析

## 发展趋势
- 多模态AI
- 边缘计算
- 联邦学习`

      let currentIndex = 0

      // 🔥 关键：模拟AI逐字符输出
      const streamInterval = setInterval(() => {
        if (currentIndex >= fullText.length) {
          clearInterval(streamInterval)

          // 完成时处理剩余节点
          this.finalizeAllNodes()
          resolve()
          return
        }

        // 每次输出1-3个字符，模拟真实的AI输出速度
        const chunkSize = Math.random() > 0.7 ? 3 : Math.random() > 0.5 ? 2 : 1
        const textChunk = fullText.slice(currentIndex, currentIndex + chunkSize)
        currentIndex += chunkSize

        // 🔥 实时解析新的文本块
        const chunks = this.parseStreamText(textChunk)

        // 立即处理解析出的数据块
        for (const chunk of chunks) {
          this.handleChunk(chunk)
        }

      }, 50) // 每50ms输出一次，模拟真实的流式速度
    })
  }

  /**
   * 传统的模拟方法（保留用于对比）
   */
  simulateStream(topic: string = 'AI技术发展'): Promise<void> {
    return new Promise((resolve) => {
      const chunks: StreamChunk[] = [
        {
          type: 'node_update',
          nodeId: 'root',
          data: { text: `${topic}思维导图` },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch1',
          parentId: 'root',
          data: { text: '核心技术' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch2',
          parentId: 'root',
          data: { text: '应用领域' },
          timestamp: Date.now()
        },
        {
          type: 'node_create',
          nodeId: 'branch3',
          parentId: 'root',
          data: { text: '发展趋势' },
          timestamp: Date.now()
        },
        {
          type: 'tree_complete',
          nodeId: '',
          data: { text: '' },
          timestamp: Date.now()
        }
      ]

      // 模拟流式传输，每300ms处理一个数据块
      chunks.forEach((chunk, index) => {
        setTimeout(() => {
          this.handleChunk(chunk)
          if (index === chunks.length - 1) {
            resolve()
          }
        }, index * 300)
      })
    })
  }
}

// 导出单例实例
export const aiStreamService = new AIStreamService()

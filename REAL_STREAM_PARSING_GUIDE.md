# 🔥 真实AI流式解析思维导图实现指南

## 核心突破：真正的流式解析

### 问题背景
传统方案假设AI返回结构化的JSON数据，但实际上AI通常返回的是**连续的文本流**，就像人类写作一样，一个字符一个字符地输出。

### 🎯 真实场景
```
AI输出过程：
# → # A → # AI → # AI技 → # AI技术 → # AI技术发展
→ 
## → ## 核 → ## 核心 → ## 核心技术
→ 
- → - 机 → - 机器 → - 机器学习
```

## 核心技术方案

### 1. 流式文本解析器

```typescript
/**
 * 🔥 核心方法：实时解析AI流式文本
 * AI每输出一个字符，我们就尝试解析
 */
private parseStreamText(newText: string): StreamChunk[] {
  const chunks: StreamChunk[] = []
  
  // 将新文本添加到缓冲区
  this.parseState.buffer += newText
  
  // 从上次处理的位置开始解析
  const textToProcess = this.parseState.buffer.slice(this.parseState.lastProcessedIndex)
  
  // 按行分割，但保留不完整的行
  const lines = textToProcess.split('\n')
  const completeLines = lines.slice(0, -1) // 完整的行
  const incompleteLine = lines[lines.length - 1] // 可能不完整的行
  
  // 处理完整的行
  for (const line of completeLines) {
    const lineChunks = this.parseLine(line)
    chunks.push(...lineChunks)
  }
  
  // 如果不完整的行看起来像是一个节点的开始，也尝试解析
  if (incompleteLine.trim()) {
    const partialChunks = this.parsePartialLine(incompleteLine)
    chunks.push(...partialChunks)
  }
  
  return chunks
}
```

### 2. 智能模式识别

#### 标题识别
```typescript
// 检测标题级别 (# ## ### 等)
const headerMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/)
if (headerMatch) {
  const level = headerMatch[1].length
  const text = headerMatch[2].trim()
  return this.createNodeFromHeader(level, text)
}
```

#### 列表项识别
```typescript
// 检测列表项 (- * + 或数字列表)
const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/)
if (listMatch) {
  const indent = listMatch[1].length
  const text = listMatch[3].trim()
  const level = Math.floor(indent / 2) + 2
  return this.createNodeFromListItem(level, text)
}
```

### 3. 节点层级管理

```typescript
/**
 * 智能节点栈管理
 * 根据markdown层级自动确定父子关系
 */
private createNodeFromHeader(level: number, text: string): StreamChunk[] {
  // 清理节点栈，移除所有级别大于等于当前级别的节点
  while (
    this.parseState.nodeStack.length > 0 && 
    this.parseState.nodeStack[this.parseState.nodeStack.length - 1].level >= level
  ) {
    // 标记上一个节点为完成
    const lastNode = this.parseState.nodeStack.pop()
    if (lastNode) {
      chunks.push({
        type: 'node_complete',
        nodeId: lastNode.id,
        data: { text: lastNode.text, isComplete: true }
      })
    }
  }
  
  // 找到正确的父节点
  const parentId = this.parseState.nodeStack.length > 0 
    ? this.parseState.nodeStack[this.parseState.nodeStack.length - 1].id 
    : 'root'
    
  // 创建新节点并加入栈
  // ...
}
```

## 🚀 使用方法

### 1. 测试真实流式解析

1. 启动项目：`pnpm dev`
2. 打开浏览器：http://localhost:5174/
3. 点击 **🔥 真实流式** 按钮
4. 观察AI逐字符输出的解析过程

### 2. 对比不同模式

- **传统模式**：预定义的结构化数据，瞬间完成
- **🔥 真实流式**：模拟AI逐字符输出，实时解析
- **快速测试**：简化版本，快速验证功能

### 3. 集成真实AI服务

```typescript
// 修改 startFetchStream 方法
private async startFetchStream(prompt: string, apiUrl: string) {
  // 重置解析状态
  this.resetParseState()
  
  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt }),
  })

  const reader = response.body?.getReader()
  const decoder = new TextDecoder()

  while (true) {
    const { done, value } = await reader.read()
    
    if (done) {
      this.finalizeAllNodes()
      break
    }

    // 🔥 关键：实时解析每一个新字符！
    const newText = decoder.decode(value, { stream: true })
    const chunks = this.parseStreamText(newText)
    
    // 立即处理解析出的数据块
    for (const chunk of chunks) {
      this.handleChunk(chunk)
    }
  }
}
```

## 🎨 视觉效果

### 实时渲染特点
1. **逐字符解析**：AI每输出一个字符，立即尝试解析
2. **智能识别**：自动识别markdown格式（标题、列表）
3. **层级管理**：根据缩进和标记自动建立父子关系
4. **实时更新**：节点内容实时更新到思维导图
5. **完成标记**：节点完成时自动标记

### 解析过程示例
```
输入: "# AI技术"
输出: 创建根节点 "AI技术"

输入: "\n## 核心技术"  
输出: 创建子节点 "核心技术"

输入: "\n- 机器学习"
输出: 创建子节点 "机器学习"

输入: "\n  - 监督学习"
输出: 创建孙子节点 "监督学习"
```

## 🔧 技术细节

### 解析状态管理
```typescript
interface ParseState {
  buffer: string                    // 文本缓冲区
  currentLevel: number             // 当前层级
  nodeStack: Array<{               // 节点栈
    id: string
    level: number
    text: string
  }>
  nodeCounter: number              // 节点计数器
  lastProcessedIndex: number       // 上次处理位置
}
```

### 性能优化
1. **增量解析**：只解析新增的文本部分
2. **缓冲区管理**：避免重复解析已处理的内容
3. **节点复用**：使用`updateData`而不是`setData`
4. **智能更新**：只在必要时触发视图更新

## 🎯 实际应用场景

### 1. ChatGPT API集成
```javascript
const response = await fetch('https://api.openai.com/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: prompt }],
    stream: true  // 🔥 关键：启用流式输出
  })
})

// 使用我们的解析器处理流式响应
const reader = response.body.getReader()
// ... 使用 parseStreamText 方法
```

### 2. 自定义AI服务
```python
# Python Flask 示例
@app.route('/api/ai/stream', methods=['POST'])
def ai_stream():
    prompt = request.json['prompt']
    
    def generate():
        # 模拟AI逐字符输出
        full_response = generate_mindmap_markdown(prompt)
        for char in full_response:
            yield char
            time.sleep(0.05)  # 模拟生成延迟
    
    return Response(generate(), mimetype='text/plain')
```

## 🌟 优势总结

1. **真实性**：完全模拟真实AI的输出过程
2. **实时性**：每个字符都能立即反映到思维导图
3. **智能性**：自动识别markdown结构
4. **灵活性**：支持各种AI服务的文本输出
5. **性能**：增量解析，高效处理大量文本

这个方案解决了AI流式生成思维导图的核心难题：**如何实时解析AI的原始文本输出并转换为结构化的思维导图数据**。

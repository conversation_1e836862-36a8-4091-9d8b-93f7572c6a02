/* eslint-disable no-undef */
// postcss.config.js
module.exports = {
  plugins: [
    require('autoprefixer'), // 自动添加浏览器前缀,例如-webkit- ，意在解决浏览器兼容性问题
    require('postcss-px-to-viewport')({
      viewportWidth: 1080, // 设计稿宽度（按实际调整）
      viewportUnit: 'vw', // 目标单位
      unitPrecision: 5, // 转换后的小数位数
      propList: ['*'], // 需要转换的CSS属性（默认全部转换）
      selectorBlackList: [
        '.ignore-vw', // 忽略的类名
        /^van-/, // 忽略 Vant 组件库（正则匹配）
      ],
      minPixelValue: 1, // 最小转换像素值
      mediaQuery: false, // 是否转换媒体查询中的px
      exclude: /node_modules/, // 排除 node_modules 中的文件
    }),
  ],
}

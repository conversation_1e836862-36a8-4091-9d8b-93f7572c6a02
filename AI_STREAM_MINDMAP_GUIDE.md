# AI流式生成思维导图实现指南

## 概述

本项目实现了AI流式生成思维导图的完整方案，支持实时渲染和增量更新。基于Vue 3 + TypeScript + simple-mind-map库构建。

## 核心特性

- ✅ **流式数据处理**: 支持AI逐步生成节点内容
- ✅ **实时渲染**: 节点内容实时更新到思维导图
- ✅ **增量更新**: 使用`updateData`方法优化性能
- ✅ **错误处理**: 完善的错误处理和状态管理
- ✅ **多种连接方式**: 支持SSE和Fetch Stream两种方式
- ✅ **模拟测试**: 内置模拟数据用于测试

## 数据格式规范

### AI返回的流式数据格式

```typescript
interface StreamChunk {
  type: 'node_create' | 'node_update' | 'node_complete' | 'tree_complete' | 'error'
  nodeId: string
  parentId?: string
  data: {
    text: string
    isComplete?: boolean
    uid?: string
    error?: string
  }
  position?: 'before' | 'after' | 'child'
  timestamp?: number
}
```

### 数据类型说明

1. **node_create**: 创建新节点
   ```json
   {
     "type": "node_create",
     "nodeId": "branch1",
     "parentId": "root",
     "data": {
       "text": "机器学习"
     }
   }
   ```

2. **node_update**: 更新节点内容
   ```json
   {
     "type": "node_update",
     "nodeId": "branch1",
     "data": {
       "text": "机器学习与深度学习"
     }
   }
   ```

3. **node_complete**: 标记节点完成
   ```json
   {
     "type": "node_complete",
     "nodeId": "branch1",
     "data": {
       "text": "机器学习与深度学习",
       "isComplete": true
     }
   }
   ```

4. **tree_complete**: 标记整个树完成
   ```json
   {
     "type": "tree_complete",
     "nodeId": "",
     "data": { "text": "" }
   }
   ```

5. **error**: 错误信息
   ```json
   {
     "type": "error",
     "nodeId": "",
     "data": {
       "error": "生成失败，请重试"
     }
   }
   ```

## 实现方案

### 1. 服务端实现（推荐方案）

#### 方案A: Server-Sent Events (SSE)

```javascript
// Node.js + Express 示例
app.get('/api/ai/stream', (req, res) => {
  const { prompt } = req.query
  
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*'
  })

  // 模拟AI生成过程
  const generateMindMap = async (prompt) => {
    // 1. 更新根节点
    res.write(`data: ${JSON.stringify({
      type: 'node_update',
      nodeId: 'root',
      data: { text: `${prompt}思维导图` }
    })}\n\n`)

    // 2. 创建分支节点
    const branches = await aiService.generateBranches(prompt)
    for (const branch of branches) {
      res.write(`data: ${JSON.stringify({
        type: 'node_create',
        nodeId: branch.id,
        parentId: 'root',
        data: { text: branch.text }
      })}\n\n`)
      
      await delay(300) // 模拟生成延迟
    }

    // 3. 完成生成
    res.write(`data: ${JSON.stringify({
      type: 'tree_complete',
      nodeId: '',
      data: { text: '' }
    })}\n\n`)
  }

  generateMindMap(prompt).catch(err => {
    res.write(`data: ${JSON.stringify({
      type: 'error',
      nodeId: '',
      data: { error: err.message }
    })}\n\n`)
  })
})
```

#### 方案B: Fetch Stream

```javascript
// Node.js + Express 示例
app.post('/api/ai/stream', async (req, res) => {
  const { prompt } = req.body
  
  res.writeHead(200, {
    'Content-Type': 'application/json',
    'Transfer-Encoding': 'chunked'
  })

  const sendChunk = (chunk) => {
    res.write(JSON.stringify(chunk) + '\n')
  }

  try {
    // AI生成逻辑
    await generateMindMapStream(prompt, sendChunk)
    res.end()
  } catch (error) {
    sendChunk({
      type: 'error',
      nodeId: '',
      data: { error: error.message }
    })
    res.end()
  }
})
```

### 2. 前端实现

前端已经实现完整的流式处理逻辑，主要包括：

- `AIStreamService`: 流式数据处理服务
- `handleStreamChunk`: 数据块处理函数
- `updateMindMap`: 思维导图更新函数
- 错误处理和状态管理

## 使用方法

### 1. 启动项目

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 2. 测试流式生成

1. 打开浏览器访问项目
2. 在输入框中输入主题（如"人工智能发展"）
3. 点击"开始生成"按钮
4. 观察思维导图实时生成过程

### 3. 集成真实AI服务

修改`aiStreamService.ts`中的`startStream`方法，将API地址指向你的AI服务：

```typescript
// 使用真实的AI API
await aiStreamService.startStream(prompt, 'https://your-ai-api.com/stream')
```

## 性能优化建议

1. **使用增量更新**: 优先使用`mindMap.updateData()`而不是`mindMap.setData()`
2. **启用性能模式**: 设置`openPerformance: true`
3. **节点缓存**: 维护节点映射表避免重复查找
4. **批量更新**: 将多个小的更新合并为批量更新
5. **防抖处理**: 对频繁的更新操作进行防抖

## 扩展功能

### 1. 添加节点样式

```typescript
// 在创建节点时添加样式
const newNode: NodeData = {
  data: {
    text: data.text,
    uid: nodeId,
    expand: true,
    // 添加样式
    fillColor: '#e8f4fd',
    borderColor: '#1890ff',
    isGenerating: !data.isComplete
  },
  children: []
}
```

### 2. 添加生成进度

```typescript
// 在StreamChunk中添加进度信息
interface StreamChunk {
  // ... 其他字段
  progress?: {
    current: number
    total: number
    percentage: number
  }
}
```

### 3. 支持富文本

```typescript
// 支持HTML格式的节点文本
{
  type: 'node_create',
  nodeId: 'branch1',
  parentId: 'root',
  data: {
    text: '<strong>机器学习</strong>',
    richText: true
  }
}
```

## 常见问题

### Q: 如何处理网络中断？
A: 实现重连机制，在连接断开时自动重试连接。

### Q: 如何优化大量节点的性能？
A: 启用性能模式，使用虚拟滚动，分批渲染节点。

### Q: 如何自定义节点样式？
A: 通过主题配置或直接在节点数据中添加样式字段。

### Q: 如何保存生成的思维导图？
A: 使用`mindMap.getData()`获取数据，然后保存到本地或服务器。

## 总结

这个方案提供了完整的AI流式生成思维导图解决方案，具有良好的扩展性和性能。你可以根据实际需求调整数据格式和处理逻辑，集成到你的AI服务中。
